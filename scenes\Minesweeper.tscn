[gd_scene load_steps=10 format=3 uid="uid://bmx80demhvjch"]

[ext_resource type="Script" path="res://scripts/Minesweeper.gd" id="1_minesweeper"]
[ext_resource type="Texture2D" uid="uid://dr1e1vm8hfr3p" path="res://assets/background.jpg" id="2_background"]
[ext_resource type="AudioStream" uid="uid://cam1ubapfebq" path="res://assets/maou_bgm_piano17.ogg" id="3_bgm"]
[ext_resource type="AudioStream" uid="uid://bqj8k2wgbtmf3" path="res://assets/maou_se_system48.ogg" id="4_tile_open_sfx"]
[ext_resource type="AudioStream" uid="uid://cqj8k2wgbtmf4" path="res://assets/maou_se_system49.ogg" id="5_chord_sfx"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ButtonHover"]
bg_color = Color(0.3, 0.4, 0.6, 0.9)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ButtonNormal"]
bg_color = Color(0.2, 0.3, 0.5, 0.8)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ButtonPressed"]
bg_color = Color(0.1, 0.2, 0.4, 0.9)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="Theme" id="Theme_Minesweeper"]
Button/colors/font_color = Color(1, 1, 1, 1)
Button/colors/font_hover_color = Color(1, 1, 0.8, 1)
Button/colors/font_pressed_color = Color(0.9, 0.9, 0.7, 1)
Button/font_sizes/font_size = 14
Button/styles/hover = SubResource("StyleBoxFlat_ButtonHover")
Button/styles/normal = SubResource("StyleBoxFlat_ButtonNormal")
Button/styles/pressed = SubResource("StyleBoxFlat_ButtonPressed")

[node name="Minesweeper" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = SubResource("Theme_Minesweeper")
script = ExtResource("1_minesweeper")

[node name="Background" type="TextureRect" parent="."]
modulate = Color(0.37918, 0.37918, 0.37918, 1)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 3993.0
offset_bottom = 7107.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.12, 0.12)
texture = ExtResource("2_background")
stretch_mode = 4

[node name="UI" type="CanvasLayer" parent="."]

[node name="BackButton" type="Button" parent="UI"]
offset_left = 20.0
offset_top = 20.0
offset_right = 120.0
offset_bottom = 56.0
text = "MENU"

[node name="MinimapPanel" type="Panel" parent="UI"]
offset_left = 376.0
offset_top = 16.0
offset_right = 520.0
offset_bottom = 172.0

[node name="FlagModeButton" type="Button" parent="UI"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -520.0
offset_top = 64.0
offset_right = -420.0
offset_bottom = 104.0
grow_horizontal = 0
text = "OFF"

[node name="FlagModeLabel" type="Label" parent="UI"]
offset_left = 20.0
offset_top = 104.0
offset_right = 120.0
offset_bottom = 120.0
text = "Tap-to-Flag"
horizontal_alignment = 1

[node name="OptionsButton" type="Button" parent="UI"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -520.0
offset_top = 20.0
offset_right = -470.0
offset_bottom = 56.0
grow_horizontal = 0
text = "⚙"

[node name="EndGameButton" type="Button" parent="UI"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -520.0
offset_top = 126.0
offset_right = -420.0
offset_bottom = 174.0
grow_horizontal = 0
text = "END GAME
GET SCORE"

[node name="InfoLabel" type="Label" parent="UI"]
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -30.0
text = "Mines: 0 | Flags: 0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="UpgradePanel" type="Panel" parent="UI"]
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -250.0
grow_horizontal = 2
grow_vertical = 0

[node name="TimeLabel" type="Label" parent="UI/UpgradePanel"]
layout_mode = 0
offset_left = 288.0
offset_top = 7.0
offset_right = 388.0
offset_bottom = 31.0
text = "Time: 00:00"
horizontal_alignment = 1

[node name="UpgradeTitle" type="Label" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 10.0
offset_top = 7.0
offset_right = 94.0
offset_bottom = 30.0
text = "UPGRADES"
horizontal_alignment = 1

[node name="CoinsLabel" type="Label" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 424.0
offset_top = 7.0
offset_right = 485.0
offset_bottom = 30.0
text = "Coins: 0"
horizontal_alignment = 1

[node name="FloodLevelLabel" type="Label" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 19.0
offset_top = 48.0
offset_right = 250.0
offset_bottom = 80.0
text = "Flood Level: 1 (Radius: 5)"
autowrap_mode = 2

[node name="FloodUpgradeButton" type="Button" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 263.0
offset_top = 43.0
offset_right = 526.0
offset_bottom = 80.0
text = "Upgrade Flood (10 coins)"
autowrap_mode = 2

[node name="CoinMultiplierLevelLabel" type="Label" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 19.0
offset_top = 90.0
offset_right = 240.0
offset_bottom = 113.0
text = "Multiplier: x1.0 (Level 1)"
autowrap_mode = 2

[node name="CoinMultiplierUpgradeButton" type="Button" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 264.0
offset_top = 88.0
offset_right = 526.0
offset_bottom = 119.0
text = "Upgrade Multiplier (15 coins)"
autowrap_mode = 2

[node name="ChordBonusLevelLabel" type="Label" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 19.0
offset_top = 130.0
offset_right = 263.0
offset_bottom = 153.0
text = "Chord Bonus: +0 (Level 1)"
autowrap_mode = 2

[node name="ChordBonusUpgradeButton" type="Button" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 262.0
offset_top = 129.0
offset_right = 526.0
offset_bottom = 160.0
text = "Upgrade Chord (20 coins)"
autowrap_mode = 2

[node name="EndGameBonusLevelLabel" type="Label" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 19.0
offset_top = 170.0
offset_right = 275.0
offset_bottom = 193.0
text = "End Game Bonus: +100 (Level 1)"
autowrap_mode = 2

[node name="EndGameBonusUpgradeButton" type="Button" parent="UI/UpgradePanel"]
layout_mode = 2
offset_left = 262.0
offset_top = 168.0
offset_right = 526.0
offset_bottom = 199.0
text = "Upgrade End Bonus (30 coins)"
autowrap_mode = 2

[node name="GameOverPanel" type="Panel" parent="UI"]
visible = false
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="GameOverTitle" type="Label" parent="UI/GameOverPanel"]
layout_mode = 2
offset_left = 10.0
offset_top = 20.0
offset_right = 590.0
offset_bottom = 80.0
text = "GAME OVER!"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="FinalScoreLabel" type="Label" parent="UI/GameOverPanel"]
layout_mode = 2
offset_left = 10.0
offset_top = 90.0
offset_right = 590.0
offset_bottom = 130.0
text = "Final Score: 0"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="StatsLabel" type="Label" parent="UI/GameOverPanel"]
layout_mode = 2
offset_left = 10.0
offset_top = 140.0
offset_right = 590.0
offset_bottom = 280.0
text = "Time: 00:00
Tiles Revealed: 0
Chords Performed: 0
Coins Earned: 0.0"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="RestartButton" type="Button" parent="UI/GameOverPanel"]
layout_mode = 2
offset_left = 50.0
offset_top = 320.0
offset_right = 250.0
offset_bottom = 360.0
text = "RESTART"

[node name="MenuButton" type="Button" parent="UI/GameOverPanel"]
layout_mode = 2
offset_left = 350.0
offset_top = 320.0
offset_right = 550.0
offset_bottom = 360.0
text = "MAIN MENU"

[node name="GameArea" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 200.0
offset_bottom = -240.0
grow_horizontal = 2
grow_vertical = 2

[node name="GridContainer" type="Control" parent="GameArea"]
anchors_preset = 0
offset_left = 32.0
offset_top = 10.0
offset_right = 504.0
offset_bottom = 480.0

[node name="MineGrid" type="Control" parent="GameArea/GridContainer"]
anchors_preset = 0

[node name="BGMPlayer" type="AudioStreamPlayer" parent="."]
stream = ExtResource("3_bgm")
autoplay = true

[node name="TileOpenSFX" type="AudioStreamPlayer" parent="."]
stream = ExtResource("4_tile_open_sfx")
volume_db = -5.0

[node name="ChordSFX" type="AudioStreamPlayer" parent="."]
stream = ExtResource("5_chord_sfx")
volume_db = -5.0

[connection signal="pressed" from="UI/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="UI/OptionsButton" to="." method="_on_options_button_pressed"]
[connection signal="pressed" from="UI/FlagModeButton" to="." method="_on_flag_mode_button_pressed"]
[connection signal="pressed" from="UI/UpgradePanel/FloodUpgradeButton" to="." method="_on_flood_upgrade_button_pressed"]
[connection signal="pressed" from="UI/UpgradePanel/CoinMultiplierUpgradeButton" to="." method="_on_coin_multiplier_upgrade_button_pressed"]
[connection signal="pressed" from="UI/UpgradePanel/ChordBonusUpgradeButton" to="." method="_on_chord_bonus_upgrade_button_pressed"]
[connection signal="pressed" from="UI/GameOverPanel/RestartButton" to="." method="_on_restart_button_pressed"]
[connection signal="pressed" from="UI/GameOverPanel/MenuButton" to="." method="_on_game_over_menu_button_pressed"]
[connection signal="gui_input" from="GameArea" to="." method="_on_game_area_input"]
